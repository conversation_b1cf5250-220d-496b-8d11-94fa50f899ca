#!/usr/bin/env python3
"""
分析对话日志的脚本
用于诊断对话更新过程中的消息一致性问题
"""

import json
import re
from datetime import datetime
from typing import Any, Dict, List


def analyze_log_file(log_file_path: str):
    """分析日志文件"""
    print(f"🔍 分析日志文件: {log_file_path}")

    try:
        with open(log_file_path, "r", encoding="utf-8") as f:
            log_content = f.read()

        # 提取消息追踪信息
        message_tracks = extract_message_tracks(log_content)

        # 提取检查点追踪信息
        checkpoint_tracks = extract_checkpoint_tracks(log_content)

        # 分析消息一致性
        analyze_message_consistency(message_tracks, checkpoint_tracks)

        # 分析检查点状态
        analyze_checkpoint_status(log_content)

        # 分析错误信息
        analyze_errors(log_content)

    except FileNotFoundError:
        print(f"❌ 日志文件不存在: {log_file_path}")
    except Exception as e:
        print(f"❌ 分析日志文件时发生错误: {str(e)}")


def extract_message_tracks(log_content: str) -> List[Dict[str, Any]]:
    """提取消息追踪信息"""
    print(f"\n📝 提取消息追踪信息...")

    # 匹配消息追踪日志的正则表达式
    pattern = r"🔍 \[消息追踪\] 创建用户消息: id=([^,]+), content=\'([^\']*)\', length=(\d+)"
    matches = re.findall(pattern, log_content)

    message_tracks = []
    for match in matches:
        message_id, content, length = match
        message_tracks.append(
            {
                "id": message_id,
                "content": content,
                "length": int(length),
                "timestamp": extract_timestamp_for_message(log_content, message_id),
            }
        )

    print(f"   找到 {len(message_tracks)} 条消息追踪记录")
    for track in message_tracks:
        print(f"   - ID: {track['id'][:8]}..., 内容: '{track['content']}', 长度: {track['length']}")

    return message_tracks


def extract_checkpoint_tracks(log_content: str) -> List[Dict[str, Any]]:
    """提取检查点追踪信息"""
    print(f"\n💾 提取检查点追踪信息...")

    # 匹配检查点追踪日志的正则表达式
    pattern = r"🔍 \[检查点追踪\] 保存检查点: thread_id=([^,]+), 消息数量=(\d+)"
    matches = re.findall(pattern, log_content)

    checkpoint_tracks = []
    for match in matches:
        thread_id, message_count = match
        checkpoint_tracks.append(
            {
                "thread_id": thread_id,
                "message_count": int(message_count),
                "timestamp": extract_timestamp_for_checkpoint(log_content, thread_id),
            }
        )

    print(f"   找到 {len(checkpoint_tracks)} 条检查点追踪记录")
    for track in checkpoint_tracks:
        print(f"   - Thread ID: {track['thread_id']}, 消息数量: {track['message_count']}")

    # 提取检查点中的最后用户消息
    pattern2 = r"🔍 \[检查点追踪\] 最后用户消息: id=([^,]+), content=\'([^\']*)\', length=(\d+)"
    matches2 = re.findall(pattern2, log_content)

    checkpoint_messages = []
    for match in matches2:
        msg_id, content, length = match
        checkpoint_messages.append({"id": msg_id, "content": content, "length": int(length)})

    print(f"   找到 {len(checkpoint_messages)} 条检查点消息记录")
    for msg in checkpoint_messages:
        print(
            f"   - ID: {msg['id'][:8] if msg['id'] != 'unknown' else 'unknown'}..., 内容: '{msg['content']}', 长度: {msg['length']}"
        )

    return checkpoint_tracks


def analyze_message_consistency(message_tracks: List[Dict[str, Any]], checkpoint_tracks: List[Dict[str, Any]]):
    """分析消息一致性"""
    print(f"\n🔍 分析消息一致性...")

    if not message_tracks:
        print(f"   ⚠️ 没有找到消息追踪记录")
        return

    # 检查是否有重复的消息内容但不同的ID
    content_to_ids = {}
    for track in message_tracks:
        content = track["content"]
        if content not in content_to_ids:
            content_to_ids[content] = []
        content_to_ids[content].append(track["id"])

    duplicates = {content: ids for content, ids in content_to_ids.items() if len(ids) > 1}

    if duplicates:
        print(f"   ⚠️ 发现重复的消息内容:")
        for content, ids in duplicates.items():
            print(f"      内容: '{content}' -> IDs: {[id[:8] + '...' for id in ids]}")
    else:
        print(f"   ✅ 没有发现重复的消息内容")

    # 检查消息长度是否与内容匹配
    length_mismatches = []
    for track in message_tracks:
        expected_length = len(track["content"])
        actual_length = track["length"]
        if expected_length != actual_length:
            length_mismatches.append(track)

    if length_mismatches:
        print(f"   ⚠️ 发现消息长度不匹配:")
        for track in length_mismatches:
            print(f"      ID: {track['id'][:8]}..., 期望长度: {len(track['content'])}, 实际长度: {track['length']}")
    else:
        print(f"   ✅ 所有消息长度都匹配")


def analyze_checkpoint_status(log_content: str):
    """分析检查点状态"""
    print(f"\n💾 分析检查点状态...")

    # 查找检查点状态相关的日志
    status_patterns = [
        (r"✅ 检查点状态正常，继续使用现有thread_id", "正常状态"),
        (r"⚠️ 检测到无效的检查点状态，强制重置对话", "强制重置"),
        (r"🚨 检测到消息内容不一致:", "消息不一致"),
        (r"🚨 检测到未响应的tool_call:", "工具调用问题"),
        (r"✅ 已重置为新的thread_id:", "重置完成"),
    ]

    for pattern, description in status_patterns:
        matches = re.findall(pattern, log_content)
        if matches:
            print(f"   - {description}: {len(matches)} 次")


def analyze_errors(log_content: str):
    """分析错误信息"""
    print(f"\n❌ 分析错误信息...")

    # 查找各种错误模式
    error_patterns = [
        (r"ERROR.*消息序列验证失败", "消息序列验证错误"),
        (r"ERROR.*检查点消息验证失败", "检查点消息验证错误"),
        (r"ERROR.*最终验证失败", "最终验证错误"),
        (r"WARNING.*忽略无法解析的检查点字符串", "检查点解析警告"),
        (r"WARNING.*消息序列修复完成", "消息序列修复警告"),
    ]

    total_errors = 0
    for pattern, description in error_patterns:
        matches = re.findall(pattern, log_content, re.IGNORECASE)
        if matches:
            print(f"   - {description}: {len(matches)} 次")
            total_errors += len(matches)

    if total_errors == 0:
        print(f"   ✅ 没有发现相关错误")


def extract_timestamp_for_message(log_content: str, message_id: str) -> str:
    """为消息提取时间戳"""
    # 这里可以实现更复杂的时间戳提取逻辑
    return "unknown"


def extract_timestamp_for_checkpoint(log_content: str, thread_id: str) -> str:
    """为检查点提取时间戳"""
    # 这里可以实现更复杂的时间戳提取逻辑
    return "unknown"


if __name__ == "__main__":
    print("=" * 60)
    print("🔍 对话日志分析工具")
    print("=" * 60)

    # 分析默认日志文件
    log_file = "app/log1.md"

    try:
        analyze_log_file(log_file)
        print(f"\n✅ 日志分析完成")

    except Exception as e:
        print(f"\n❌ 分析过程中发生错误: {str(e)}")
        import traceback

        traceback.print_exc()
