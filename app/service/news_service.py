"""
新闻服务模块
处理所有新闻相关的业务逻辑，包括新闻爬取、搜索、统计和关键词管理
"""

import threading


from configs.lion_config import get_value
from pydantic import BaseModel
from utils.logger import logger


class NewsQueryRequest(BaseModel):
    """语义搜索新闻请求模型"""

    keyword: str  # 搜索关键词（必填）
    size: int = 10  # 返回结果数量，默认10条


class NewsService:
    """新闻服务类"""

    def __init__(self):
        pass

    def trigger_news_crawler(self) -> dict:
        """手动触发新闻爬取和AI处理"""
        try:
            from service.ESmemory.es_news_crawler import main

            # 异步执行爬取任务
            t = threading.Thread(target=main, daemon=True)
            t.start()

            return {"success": True, "message": "新闻爬取任务已触发，正在后台执行"}
        except Exception as e:
            logger.error(f"触发新闻爬取失败: {e}")
            return {"success": False, "message": f"触发失败: {str(e)}"}

    def get_news_crawler_status(self, news_crawler_thread) -> dict:
        """查看新闻爬虫线程状态"""
        try:
            # 获取配置的爬取间隔
            crawl_interval = get_value("xiaomei.humanrelation.news_crawl_interval", "360")

            status = {
                "thread_exists": news_crawler_thread is not None,
                "thread_alive": news_crawler_thread.is_alive() if news_crawler_thread else False,
                "thread_name": news_crawler_thread.name if news_crawler_thread else None,
                "crawl_interval_minutes": crawl_interval,
                "crawl_interval_hours": float(crawl_interval) / 60,
            }

            return {"success": True, "status": status}
        except Exception as e:
            logger.error(f"获取新闻爬虫状态失败: {e}")
            return {"success": False, "message": f"获取状态失败: {str(e)}"}

    def get_news_stats(self) -> dict:
        """查看AI处理的新闻统计信息"""
        try:
            from service.ESmemory.es_memory_client import client

            index_name = get_value("humanrelation.news_index_name", "humanrelation_news_v3")

            # 查询总数
            total_query = {"match_all": {}}
            total_resp = client.count(index=index_name, body={"query": total_query})
            total_count = total_resp.get("count", 0)

            # 查询AI判断为适合的数量
            suitable_query = {"term": {"ai_judgment.suitable": True}}
            suitable_resp = client.count(index=index_name, body={"query": suitable_query})
            suitable_count = suitable_resp.get("count", 0)

            # 查询AI判断为不适合的数量
            unsuitable_query = {"term": {"ai_judgment.suitable": False}}
            unsuitable_resp = client.count(index=index_name, body={"query": unsuitable_query})
            unsuitable_count = unsuitable_resp.get("count", 0)

            # 查询有关键词的数量
            keywords_query = {"exists": {"field": "generated_keywords"}}
            keywords_resp = client.count(index=index_name, body={"query": keywords_query})
            keywords_count = keywords_resp.get("count", 0)

            # 查询最近添加的新闻
            recent_resp = client.search(
                index=index_name, body={"query": {"match_all": {}}, "size": 10, "sort": [{"created_at": {"order": "desc"}}]}
            )

            recent_news = []
            for hit in recent_resp["hits"]["hits"]:
                source = hit["_source"]
                recent_news.append(
                    {
                        "id": hit["_id"],
                        "title": source.get("title", ""),
                        "created_at": source.get("created_at", ""),
                        "suitable": source.get("ai_judgment", {}).get("suitable", None),
                        "confidence": source.get("ai_judgment", {}).get("confidence", None),
                        "keywords_count": len(source.get("generated_keywords", [])),
                        "source_keyword": source.get("source_keyword", ""),
                    }
                )

            return {
                "success": True,
                "stats": {
                    "total": total_count,
                    "suitable": suitable_count,
                    "unsuitable": unsuitable_count,
                    "with_keywords": keywords_count,
                    "suitable_percentage": round(suitable_count / total_count * 100, 2) if total_count > 0 else 0,
                },
                "recent_news": recent_news,
            }
        except Exception as e:
            logger.error(f"获取新闻统计失败: {e}")
            return {"success": False, "message": f"获取统计失败: {str(e)}"}

    def search_news(self, request: NewsQueryRequest) -> dict:
        """语义搜索新闻接口，基于AI理解搜索意图"""
        try:
            from rag_retriever import rag_retriever

            # 验证搜索关键词
            if not request.keyword or not request.keyword.strip():
                return {
                    "success": False,
                    "message": "搜索关键词不能为空",
                    "news": [],
                    "search_type": "semantic"
                }

            logger.info(f"🔍 RAG语义搜索: '{request.keyword}'")

            # 执行语义搜索
            results = rag_retriever.semantic_search(request.keyword, top_k=request.size)

            # 处理结果
            news_list = []
            for result in results:
                news_list.append(
                    {
                        "id": result.get("doc_id", ""),
                        "title": result.get("title", ""),
                        "content_preview": (
                            result.get("content", "")[:200] + "..."
                            if len(result.get("content", "")) > 200
                            else result.get("content", "")
                        ),
                        "tags": result.get("tags", []),
                        "created_at": result.get("created_at", ""),
                        "source_url": result.get("source_url", ""),
                        "similarity": result.get("similarity", 0),
                        "ai_judgment": result.get("ai_judgment", {}),
                        "generated_keywords": result.get("generated_keywords", []),
                        "source_keyword": result.get("source_keyword", ""),
                    }
                )

            return {
                "success": True,
                "total": len(news_list),
                "news": news_list,
                "search_type": "semantic"
            }

        except Exception as e:
            logger.error(f"语义搜索失败: {e}")
            return {"success": False, "message": f"语义搜索失败: {str(e)}"}

    def get_all_keywords(self) -> dict:
        """获取当前配置的所有关键词列表"""
        try:
            from service.ESmemory.es_news_crawler import get_news_config

            # 获取当前配置
            config, keywords_config, ai_config = get_news_config()

            # 提取关键词列表
            keywords = []
            for item in keywords_config.get("keywords", []):
                keywords.append(
                    {"keyword": item.get("keyword", ""), "tags": item.get("tags", []), "enabled": item.get("enabled", True)}
                )

            # 获取生成的关键词建议
            try:
                from service.ESmemory.es_memory_client import client

                keywords_index_name = "humanrelation_generated_keywords"

                # 查询未使用的关键词建议
                query = {"bool": {"must": [{"term": {"used": False}}]}}

                resp = client.search(
                    index=keywords_index_name, body={"query": query, "size": 50, "sort": [{"timestamp": {"order": "desc"}}]}
                )

                suggestions = []
                for hit in resp["hits"]["hits"]:
                    doc = hit["_source"]
                    suggestions.append(
                        {
                            "id": hit["_id"],
                            "source_title": doc.get("source_title", ""),
                            "keywords": doc.get("generated_keywords", []),
                            "timestamp": doc.get("timestamp", ""),
                        }
                    )
            except Exception as e:
                logger.error(f"获取关键词建议失败: {e}")
                suggestions = []

            return {
                "success": True,
                "keywords": keywords,
                "total": len(keywords),
                "suggestions": suggestions,
                "suggestions_count": len(suggestions),
            }
        except Exception as e:
            logger.error(f"获取关键词列表失败: {e}")
            return {"success": False, "message": f"获取失败: {str(e)}"}


# 创建全局的新闻服务实例
news_service = NewsService()
