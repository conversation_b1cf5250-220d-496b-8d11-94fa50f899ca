"""
增强版重复新闻清理脚本
使用循环方式处理，避免超时问题
支持清理URL重复和内容相似的新闻
"""

import sys
import os
import re
import urllib.parse
from difflib import SequenceMatcher

# 路径设置
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = current_dir
while not os.path.exists(os.path.join(project_root, 'app', 'configs')):
    parent = os.path.dirname(project_root)
    if parent == project_root:
        break
    project_root = parent

sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'app'))

try:
    from configs.lion_config import get_value
    from service.ESmemory.es_memory_client import client
except ImportError:
    try:
        from app.configs.lion_config import get_value
        from app.service.ESmemory.es_memory_client import client
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        sys.exit(1)


def clean_text(text: str) -> str:
    """清理文本，移除HTML标签和多余空白"""
    if not text:
        return ""

    # 移除HTML标签
    text = re.sub(r'<[^>]+>', '', text)
    # 移除多余空白
    text = re.sub(r'\s+', ' ', text).strip()
    return text


def calculate_text_similarity(text1: str, text2: str) -> float:
    """计算两个文本的相似度"""
    if not text1 or not text2:
        return 0.0

    clean_text1 = clean_text(text1)
    clean_text2 = clean_text(text2)

    if not clean_text1 or not clean_text2:
        return 0.0

    return SequenceMatcher(None, clean_text1, clean_text2).ratio()


def check_content_similarity(title1: str, content1: str, title2: str, content2: str) -> tuple:
    """
    检查两条新闻的内容相似度
    返回 (is_similar: bool, similarity_score: float, reason: str)
    """
    title_similarity = calculate_text_similarity(title1, title2)

    # 计算内容相似度（取前500字符比较）
    content1_short = content1[:500] if content1 else ""
    content2_short = content2[:500] if content2 else ""
    content_similarity = calculate_text_similarity(content1_short, content2_short)

    # 相似度判断阈值
    title_threshold = 0.8
    content_threshold = 0.7

    if title_similarity > title_threshold and content_similarity > content_threshold:
        return True, max(title_similarity, content_similarity), f"标题相似度:{title_similarity:.3f}, 内容相似度:{content_similarity:.3f}"
    elif title_similarity > 0.95:  # 标题几乎完全相同
        return True, title_similarity, f"标题高度相似:{title_similarity:.3f}"
    elif content_similarity > 0.85:  # 内容高度相似
        return True, content_similarity, f"内容高度相似:{content_similarity:.3f}"

    return False, max(title_similarity, content_similarity), f"标题相似度:{title_similarity:.3f}, 内容相似度:{content_similarity:.3f}"


def simple_cleanup():
    """循环清理重复新闻，每次处理少量数据避免超时"""
    index_name = get_value("humanrelation.news_index_name", "humanrelation_news_v3")

    total_deleted = 0
    round_num = 0

    print("🔍 开始循环清理重复新闻...")

    while True:
        round_num += 1
        print(f"\n📦 第 {round_num} 轮清理...")

        # 每轮只查找少量重复URL，避免超时
        query = {
            "size": 0,
            "aggs": {
                "duplicate_urls": {
                    "terms": {
                        "field": "source_url",
                        "min_doc_count": 2,
                        "size": 50  # 每轮只处理50个重复URL
                    }
                }
            }
        }

        try:
            response = client.search(index=index_name, body=query)
            buckets = response["aggregations"]["duplicate_urls"]["buckets"]

            if not buckets:
                print(f"✅ 第 {round_num} 轮没有发现重复URL，清理完成！")
                break

            print(f"📊 第 {round_num} 轮发现 {len(buckets)} 个重复URL")

            round_deleted = 0

            # 处理这一轮的重复URL
            for i, bucket in enumerate(buckets):
                url = bucket["key"]
                count = bucket["doc_count"]

                print(f"  📰 [{i+1}/{len(buckets)}] {url[:60]}... ({count}个重复)")

                # 清理这个URL的重复
                deleted = clean_url_duplicates(index_name, url)
                round_deleted += deleted

                if deleted > 0:
                    print(f"      ✅ 删除了 {deleted} 个重复")

            total_deleted += round_deleted
            print(f"📊 第 {round_num} 轮完成，删除 {round_deleted} 个重复（累计 {total_deleted} 个）")

            # 如果这轮没有删除任何重复，说明完成了
            if round_deleted == 0:
                print("✅ 没有更多重复需要清理")
                break

        except Exception as e:
            print(f"❌ 第 {round_num} 轮处理失败: {e}")
            break

    print(f"\n🎉 清理完成！总共删除 {total_deleted} 个重复新闻")
    return total_deleted


def clean_url_duplicates(index_name, url):
    """清理特定URL的重复新闻"""
    try:
        # 查询该URL的所有文档
        search_query = {
            "query": {"term": {"source_url": url}},
            "sort": [{"created_at": {"order": "asc"}}],
            "size": 50,
            "_source": ["created_at"]
        }

        response = client.search(index=index_name, body=search_query)
        hits = response["hits"]["hits"]

        if len(hits) <= 1:
            return 0

        # 保留第一个，删除其余的
        delete_docs = hits[1:]
        deleted_count = 0

        for doc in delete_docs:
            try:
                client.delete(index=index_name, id=doc["_id"])
                deleted_count += 1
            except Exception as e:
                print(f"        ❌ 删除失败: {e}")

        return deleted_count

    except Exception as e:
        print(f"        ❌ 处理URL失败: {e}")
        return 0


def clean_content_similarity():
    """清理内容相似的重复新闻"""
    index_name = get_value("humanrelation.news_index_name", "humanrelation_news_v3")

    print("🔍 开始查找内容相似的新闻...")
    total_deleted = 0
    batch_size = 100
    processed_ids = set()

    # 分批获取新闻进行比较
    scroll_query = {
        "query": {"match_all": {}},
        "sort": [{"created_at": {"order": "desc"}}],
        "_source": ["title", "content", "created_at", "source_url"]
    }

    try:
        # 初始化滚动查询
        response = client.search(
            index=index_name,
            body=scroll_query,
            scroll="5m",
            size=batch_size
        )

        scroll_id = response['_scroll_id']
        hits = response['hits']['hits']
        total_docs = response['hits']['total']['value']
        processed_count = 0

        print(f"📊 总共需要检查 {total_docs:,} 条新闻")

        while hits:
            current_batch = []
            for hit in hits:
                if hit['_id'] not in processed_ids:
                    current_batch.append({
                        'id': hit['_id'],
                        'title': hit['_source'].get('title', ''),
                        'content': hit['_source'].get('content', ''),
                        'created_at': hit['_source'].get('created_at', ''),
                        'source_url': hit['_source'].get('source_url', '')
                    })
                    processed_ids.add(hit['_id'])

            # 在当前批次内查找相似内容
            batch_deleted = find_and_delete_similar_in_batch(index_name, current_batch)
            total_deleted += batch_deleted

            processed_count += len(current_batch)
            if processed_count % 200 == 0:
                print(f"  📈 已检查 {processed_count:,}/{total_docs:,} 条新闻，删除 {total_deleted} 条重复")

            # 获取下一批
            response = client.scroll(scroll_id=scroll_id, scroll="5m")
            scroll_id = response['_scroll_id']
            hits = response['hits']['hits']

        # 清理滚动上下文
        client.clear_scroll(scroll_id=scroll_id)

        print(f"✅ 内容相似度检查完成，共删除 {total_deleted} 条重复新闻")
        return total_deleted

    except Exception as e:
        print(f"❌ 内容相似度检查失败: {e}")
        return 0


def find_and_delete_similar_in_batch(index_name, news_batch):
    """在批次内查找并删除相似内容"""
    deleted_count = 0

    for i, news1 in enumerate(news_batch):
        if not news1:  # 可能已被删除
            continue

        similar_found = []

        # 与批次内其他新闻比较
        for j, news2 in enumerate(news_batch[i+1:], i+1):
            if not news2:  # 可能已被删除
                continue

            is_similar, similarity_score, reason = check_content_similarity(
                news1['title'], news1['content'],
                news2['title'], news2['content']
            )

            if is_similar:
                similar_found.append({
                    'index': j,
                    'news': news2,
                    'similarity': similarity_score,
                    'reason': reason
                })

        # 如果找到相似内容，保留最早的，删除其他的
        if similar_found:
            # 按创建时间排序，保留最早的
            all_similar = [{'index': i, 'news': news1, 'created_at': news1['created_at']}]
            for item in similar_found:
                all_similar.append({
                    'index': item['index'],
                    'news': item['news'],
                    'created_at': item['news']['created_at']
                })

            # 按时间排序
            all_similar.sort(key=lambda x: x['created_at'])

            # 保留第一个（最早的），删除其他的
            keep_item = all_similar[0]
            delete_items = all_similar[1:]

            print(f"  🔍 发现内容相似新闻组 (共{len(all_similar)}条):")
            print(f"    保留: {keep_item['news']['title'][:50]}... ({keep_item['created_at']})")

            for item in delete_items:
                try:
                    client.delete(index=index_name, id=item['news']['id'])
                    print(f"    删除: {item['news']['title'][:50]}... ({item['created_at']})")
                    deleted_count += 1
                    # 从批次中移除已删除的项
                    news_batch[item['index']] = None
                except Exception as e:
                    print(f"    ❌ 删除失败: {e}")

    return deleted_count


def show_current_stats():
    """显示当前统计"""
    index_name = get_value("humanrelation.news_index_name", "humanrelation_news_v3")

    try:
        # 总数
        total_resp = client.count(index=index_name, body={"query": {"match_all": {}}})
        total_count = total_resp.get("count", 0)

        # 重复URL数量（简单查询）
        query = {
            "size": 0,
            "aggs": {
                "duplicate_urls": {
                    "terms": {
                        "field": "source_url",
                        "min_doc_count": 2,
                        "size": 1  # 只要知道有没有重复即可
                    }
                }
            }
        }

        response = client.search(index=index_name, body=query)
        has_duplicates = len(response["aggregations"]["duplicate_urls"]["buckets"]) > 0

        print(f"📊 当前统计:")
        print(f"   总新闻数量: {total_count:,}")
        print(f"   重复状态: {'⚠️ 仍有重复' if has_duplicates else '✅ 无重复'}")

    except Exception as e:
        print(f"❌ 获取统计失败: {e}")


if __name__ == "__main__":
    print("=== 增强版重复新闻清理工具 ===\n")

    # 显示清理前状态
    show_current_stats()

    print("\n📋 清理选项:")
    print("  1. 清理URL重复的新闻 (快速)")
    print("  2. 清理内容相似的新闻 (较慢但更全面)")
    print("  3. 两种都清理 (推荐)")

    choice = input("\n请选择清理方式 (1/2/3): ").strip()

    if choice not in ['1', '2', '3']:
        print("❌ 无效选择，退出")
        sys.exit(0)

    total_deleted = 0

    if choice in ['1', '3']:
        print(f"\n🔄 第一步：清理URL重复的新闻...")
        confirm = input("⚠️  开始清理URL重复新闻吗？(y/N): ")
        if confirm.lower() == 'y':
            deleted_url = simple_cleanup()
            total_deleted += deleted_url
            print(f"✅ URL重复清理完成，删除 {deleted_url} 条重复")
        else:
            print("⏭️  跳过URL重复清理")

    if choice in ['2', '3']:
        print(f"\n🔄 {'第二步' if choice == '3' else ''}：清理内容相似的新闻...")
        confirm = input("⚠️  开始清理内容相似新闻吗？(这可能需要较长时间) (y/N): ")
        if confirm.lower() == 'y':
            deleted_content = clean_content_similarity()
            total_deleted += deleted_content
            print(f"✅ 内容相似清理完成，删除 {deleted_content} 条重复")
        else:
            print("⏭️  跳过内容相似清理")

    # 显示清理后状态
    if total_deleted > 0:
        print(f"\n=== 清理后统计 ===")
        show_current_stats()
        print(f"\n🎉 总计删除 {total_deleted} 条重复新闻！")
    else:
        print(f"\n✅ 没有删除任何新闻")

    print(f"\n🎉 任务完成！")
