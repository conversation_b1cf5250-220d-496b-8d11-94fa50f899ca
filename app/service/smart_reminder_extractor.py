"""
智能提醒提取服务

功能：
1. 从对话内容中智能提取需要设置提醒的信息
2. 自动识别生日、约会、会议、重要事件等
3. 解析时间、人物、事件内容
4. 自动创建相应的提醒
"""

import json
import re
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

from configs.lion_config import get_value
from service.ai_client import send_to_ai
from service.reminder_service import reminder_service
from utils.logger import logger


class SmartReminderExtractor:
    """智能提醒提取器"""

    def __init__(self):
        # 获取AI模型配置
        self.model = get_value("humanrelation.smart_reminder_model", "gpt-4o-mini")

        # 提醒类型配置
        self.reminder_types = {
            "birthday": {"advance_days": [7, 1], "template": "🎂 {person}的生日快到了"},
            "meeting": {"advance_minutes": [30, 10], "template": "📅 会议提醒：{event}"},
            "appointment": {"advance_minutes": [60, 15], "template": "📋 约会提醒：{event}"},
            "deadline": {"advance_days": [3, 1], "template": "⏰ 截止日期提醒：{event}"},
            "event": {"advance_hours": [24, 2], "template": "🎯 事件提醒：{event}"},
            "travel": {"advance_days": [7, 1], "template": "✈️ 出行提醒：{event}"},
            "health": {"advance_hours": [12, 1], "template": "💊 健康提醒：{event}"},
            "anniversary": {"advance_days": [7, 1], "template": "💕 纪念日提醒：{event}"}
        }

    def extract_reminders_from_conversation(self, conversation_text: str, user_id: str) -> List[Dict]:
        """
        从对话文本中提取提醒信息

        Args:
            conversation_text: 对话文本
            user_id: 用户ID

        Returns:
            List[Dict]: 提取到的提醒信息列表
        """
        try:
            logger.info(f"开始从对话中提取提醒信息，用户: {user_id}")

            # 1. 使用AI分析对话内容
            extracted_info = self._ai_extract_reminder_info(conversation_text)

            if not extracted_info:
                return []

            # 2. 处理提取的信息
            reminders = []
            for info in extracted_info:
                try:
                    reminder = self._process_extracted_info(info, user_id)
                    if reminder:
                        reminders.append(reminder)
                except Exception as e:
                    logger.error(f"处理提取信息失败: {e}, 信息: {info}")
                    continue

            logger.info(f"成功提取 {len(reminders)} 个提醒信息")
            return reminders

        except Exception as e:
            logger.error(f"从对话中提取提醒信息失败: {e}")
            return []

    def _ai_extract_reminder_info(self, conversation_text: str) -> List[Dict]:
        """
        使用AI从对话中提取提醒相关信息

        Args:
            conversation_text: 对话文本

        Returns:
            List[Dict]: AI提取的信息列表
        """
        try:
            # AI提示词
            prompt = get_value(
                "humanrelation.smart_reminder_extraction_prompt")

            # 构建AI请求，包含当前时间信息
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M (%A)")
            user_message = f"""当前时间：{current_time}

请分析以下对话内容，提取需要设置提醒的信息：

{conversation_text}

注意：请根据当前时间准确推算相对时间表达（如"下周三"等）的具体日期。"""

            query_input = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": user_message}
                ],
                "stream": False,
                "temperature": 0.3,
                "max_tokens": 2000,
            }

            response = send_to_ai(query_input, need_logger=False)
            if not response:
                logger.warning("AI提醒提取服务调用失败")
                return []

            response_data = json.loads(response.text)
            ai_content = response_data["choices"][0]["message"]["content"].strip()

            # 添加调试日志
            logger.info(f"AI原始响应内容: {ai_content}")

            # 尝试解析AI返回的JSON
            try:
                # 更强健的JSON提取逻辑
                json_content = ai_content

                # 处理markdown格式
                if "```json" in ai_content:
                    json_start = ai_content.find("```json") + 7
                    json_end = ai_content.find("```", json_start)
                    if json_end != -1:
                        json_content = ai_content[json_start:json_end].strip()
                elif "```" in ai_content:
                    json_start = ai_content.find("```") + 3
                    json_end = ai_content.find("```", json_start)
                    if json_end != -1:
                        json_content = ai_content[json_start:json_end].strip()

                # 尝试从文本中提取JSON数组
                import re
                json_pattern = r'\[.*?\]'
                json_matches = re.findall(json_pattern, json_content, re.DOTALL)

                if json_matches:
                    # 使用找到的第一个JSON数组
                    json_content = json_matches[0].strip()
                    logger.info(f"使用正则提取的JSON: {json_content}")
                elif not json_content.strip().startswith('['):
                    # 如果还是没有找到合适的JSON，尝试查找第一个[开始的内容
                    start_bracket = json_content.find('[')
                    if start_bracket != -1:
                        # 找到最后一个]
                        end_bracket = json_content.rfind(']')
                        if end_bracket != -1 and end_bracket > start_bracket:
                            json_content = json_content[start_bracket:end_bracket+1].strip()

                logger.info(f"提取的JSON内容: {json_content}")
                extracted_info = json.loads(json_content)

                # 验证返回格式
                if not isinstance(extracted_info, list):
                    logger.warning(f"AI返回的不是数组格式: {type(extracted_info)}")
                    return []

                logger.info(f"AI成功提取到 {len(extracted_info)} 条提醒信息")
                return extracted_info

            except json.JSONDecodeError as e:
                logger.error(f"AI返回的JSON格式错误: {e}, 内容: {ai_content}")
                return []

        except Exception as e:
            logger.error(f"AI提取提醒信息失败: {e}")
            return []

    def _process_extracted_info(self, info: Dict, user_id: str) -> Optional[Dict]:
        """
        处理AI提取的信息，转换为提醒格式

        Args:
            info: AI提取的信息
            user_id: 用户ID

        Returns:
            Optional[Dict]: 处理后的提醒信息
        """
        try:
            # 验证必需字段
            if not all(key in info for key in ["type", "event_description", "date_time"]):
                logger.warning(f"提取信息缺少必需字段: {info}")
                return None

            # 检查置信度
            confidence = info.get("confidence", 0.8)
            if confidence < 0.6:
                logger.info(f"置信度过低，跳过提醒: {confidence}")
                return None

            # 解析时间 - 支持多种格式
            event_time = self._parse_datetime(info["date_time"])
            if not event_time:
                logger.error(f"无法解析时间格式: {info['date_time']}")
                return None

            # 检查时间是否为未来时间
            if event_time <= datetime.now():
                logger.info(f"事件时间已过期，跳过: {info['date_time']}")
                return None

            # 获取提醒类型配置
            reminder_type = info["type"]
            if reminder_type not in self.reminder_types:
                reminder_type = "event"  # 默认使用通用事件类型

            type_config = self.reminder_types[reminder_type]

            # 生成提醒内容
            person = info.get("person", "")
            event_desc = info["event_description"]
            location = info.get("location", "")

            # 构建提醒文本模板
            reminder_text = type_config["template"].format(
                person=person,
                event=event_desc
            )

            if location:
                reminder_text += f"，地点：{location}"

            # 计算提醒时间（根据类型设置不同的提前时间）
            reminder_times = self._calculate_reminder_times(event_time, type_config)

            # 构建提醒信息
            reminder_info = {
                "user_id": user_id,
                "event_description": event_desc,
                "event_time": event_time,
                "reminder_text": reminder_text,
                "reminder_times": reminder_times,
                "person": person,
                "location": location,
                "type": reminder_type,
                "confidence": confidence,
                "context": info.get("context", "")
            }

            return reminder_info

        except Exception as e:
            logger.error(f"处理提取信息失败: {e}")
            return None

    def _calculate_reminder_times(self, event_time: datetime, type_config: Dict) -> List[datetime]:
        """
        根据事件类型计算提醒时间

        Args:
            event_time: 事件时间
            type_config: 类型配置

        Returns:
            List[datetime]: 提醒时间列表
        """
        reminder_times = []

        # 按天提前
        if "advance_days" in type_config:
            for days in type_config["advance_days"]:
                reminder_time = event_time - timedelta(days=days)
                if reminder_time > datetime.now():
                    reminder_times.append(reminder_time)

        # 按小时提前
        elif "advance_hours" in type_config:
            for hours in type_config["advance_hours"]:
                reminder_time = event_time - timedelta(hours=hours)
                if reminder_time > datetime.now():
                    reminder_times.append(reminder_time)

        # 按分钟提前
        elif "advance_minutes" in type_config:
            for minutes in type_config["advance_minutes"]:
                reminder_time = event_time - timedelta(minutes=minutes)
                if reminder_time > datetime.now():
                    reminder_times.append(reminder_time)

        return reminder_times

    def _parse_datetime(self, date_time_str: str) -> Optional[datetime]:
        """
        智能解析多种时间格式

        Args:
            date_time_str: 时间字符串

        Returns:
            Optional[datetime]: 解析后的时间对象
        """
        if not date_time_str:
            return None

        # 支持的时间格式列表
        formats = [
            "%Y-%m-%d %H:%M",      # 2025-08-13 15:30
            "%Y-%m-%d",            # 2025-08-13
            "%Y/%m/%d %H:%M",      # 2025/08/13 15:30
            "%Y/%m/%d",            # 2025/08/13
            "%m-%d %H:%M",         # 08-13 15:30 (假设当前年份)
            "%m/%d %H:%M",         # 08/13 15:30 (假设当前年份)
            "%m-%d",               # 08-13 (假设当前年份)
            "%m/%d",               # 08/13 (假设当前年份)
        ]

        for fmt in formats:
            try:
                parsed_time = datetime.strptime(date_time_str, fmt)

                # 如果没有年份，假设是当前年份
                if "%Y" not in fmt:
                    current_year = datetime.now().year
                    parsed_time = parsed_time.replace(year=current_year)

                    # 如果时间已经过去，假设是明年
                    if parsed_time <= datetime.now():
                        parsed_time = parsed_time.replace(year=current_year + 1)

                return parsed_time

            except ValueError:
                continue

        # 如果都无法解析，尝试相对时间处理
        return self._parse_relative_time(date_time_str)

    def _parse_relative_time(self, time_str: str) -> Optional[datetime]:
        """
        解析相对时间表达

        Args:
            time_str: 相对时间字符串

        Returns:
            Optional[datetime]: 解析后的时间对象
        """
        try:
            now = datetime.now()
            time_str = time_str.lower().strip()

            # 处理"明天"、"后天"等
            if "明天" in time_str:
                return now + timedelta(days=1)
            elif "后天" in time_str:
                return now + timedelta(days=2)
            elif "下周" in time_str:
                # 获取下周的日期
                days_ahead = 7 - now.weekday()  # 到下周一的天数
                if "一" in time_str or "1" in time_str:
                    return now + timedelta(days=days_ahead)
                elif "二" in time_str or "2" in time_str:
                    return now + timedelta(days=days_ahead + 1)
                elif "三" in time_str or "3" in time_str:
                    return now + timedelta(days=days_ahead + 2)
                elif "四" in time_str or "4" in time_str:
                    return now + timedelta(days=days_ahead + 3)
                elif "五" in time_str or "5" in time_str:
                    return now + timedelta(days=days_ahead + 4)
                elif "六" in time_str or "6" in time_str:
                    return now + timedelta(days=days_ahead + 5)
                elif "日" in time_str or "天" in time_str or "7" in time_str:
                    return now + timedelta(days=days_ahead + 6)
                else:
                    # 默认下周一
                    return now + timedelta(days=days_ahead)

            return None

        except Exception as e:
            logger.warning(f"解析相对时间失败: {e}, 输入: {time_str}")
            return None

    def create_reminders_automatically(self, reminder_info: Dict) -> List[str]:
        """
        自动创建提醒

        Args:
            reminder_info: 提醒信息

        Returns:
            List[str]: 创建的提醒ID列表
        """
        try:
            created_reminders = []

            for reminder_time in reminder_info["reminder_times"]:
                # 构建提醒数据
                reminder_data = {
                    "user_id": reminder_info["user_id"],
                    "base_event_date": reminder_info["event_time"],
                    "next_trigger_time": reminder_time,
                    "subject_person_id": None,  # 可以后续通过人员匹配来设置
                    "reminder_text_template": reminder_info["reminder_text"],
                    "advance_notice_config": None,
                    "recurrence_rule": None,
                    "status": "active"
                }

                # 调用提醒服务创建提醒
                result = reminder_service.add_reminder(reminder_data)

                if result.get("success"):
                    reminder_id = result.get("reminder_id")
                    created_reminders.append(str(reminder_id))
                    logger.info(f"成功创建智能提醒: {reminder_id}")
                else:
                    logger.error(f"创建智能提醒失败: {result.get('message')}")

            return created_reminders

        except Exception as e:
            logger.error(f"自动创建提醒失败: {e}")
            return []

    def analyze_and_create_reminders(self, conversation_text: str, user_id: str) -> Dict:
        """
        完整的分析和创建提醒流程

        Args:
            conversation_text: 对话文本
            user_id: 用户ID

        Returns:
            Dict: 处理结果
        """
        try:
            # 1. 提取提醒信息
            extracted_reminders = self.extract_reminders_from_conversation(conversation_text, user_id)

            if not extracted_reminders:
                return {
                    "success": True,
                    "message": "未发现需要设置提醒的信息",
                    "reminders_created": 0,
                    "reminder_ids": []
                }

            # 2. 创建提醒
            all_reminder_ids = []
            successful_count = 0

            for reminder_info in extracted_reminders:
                reminder_ids = self.create_reminders_automatically(reminder_info)
                if reminder_ids:
                    all_reminder_ids.extend(reminder_ids)
                    successful_count += 1

            return {
                "success": True,
                "message": f"成功创建 {len(all_reminder_ids)} 个智能提醒",
                "reminders_created": len(all_reminder_ids),
                "reminder_ids": all_reminder_ids,
                "extracted_info_count": len(extracted_reminders),
                "successful_events": successful_count
            }

        except Exception as e:
            logger.error(f"智能提醒分析和创建失败: {e}")
            return {
                "success": False,
                "message": f"智能提醒处理失败: {str(e)}",
                "reminders_created": 0,
                "reminder_ids": []
            }


# 全局实例
smart_reminder_extractor = SmartReminderExtractor()
