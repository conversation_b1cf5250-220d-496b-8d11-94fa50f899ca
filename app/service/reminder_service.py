"""
提醒服务模块

负责处理所有与提醒相关的业务逻辑，包括：
- 后台提醒任务管理
- 线程生命周期管理
- 提醒内容生成
- 提醒推送处理
"""

import json
import threading
import time
from datetime import datetime
from typing import Any, Dict, List, Optional

from configs.lion_config import get_value
from my_mysql.entity.reminders import (
    delete_reminder_by_id,
    insert_reminder,
    query_reminder_by_id,
    query_reminders_by_status_and_time,
    query_reminders_by_user,
    update_reminder as db_update_reminder,
    update_reminder_status,
)
from pydantic import BaseModel
from tools.reminder_tool import calculate_advance_notice_config, generate_display_text
from utils.logger import logger


# 全局变量用于追踪后台线程
reminder_thread = None
thread_start_time = None

PROMPT_TEMPLATE = get_value(
    "reminder.prompt.template",
    "请根据以下信息生成一条温馨的提醒：{reminder_text_template}",
)


class ReminderService:
    """提醒服务类"""

    def __init__(self):
        self.thread = None
        self.start_time = None

    def reminder_background_task(self):
        """
        后台任务：每10分钟查询一次需要处理的提醒
        """
        # logger.info("后台提醒任务线程开始运行")
        while True:
            try:
                # 查询状态为'active'且触发时间已到的提醒
                current_time = datetime.now()  # TODO: 这里的时间需要弄好，决定好时间的格式，比如YYYY-MM-DD
                active_reminders = query_reminders_by_status_and_time("active", current_time)

                if active_reminders:
                    # logger.info(f"发现 {len(active_reminders)} 条需要处理的提醒")
                    columns = [
                        "reminder_id",
                        "user_id",
                        "subject_person_id",
                        "reminder_text_template",
                        "base_event_date",
                        "advance_notice_config",
                        "recurrence_rule",
                        "next_trigger_time",
                        "status",
                        "created_at",
                        "updated_at",
                    ]
                    for reminder in active_reminders:
                        reminder_dict = dict(zip(columns, reminder))
                        # logger.info(f"提醒ID: {reminder_dict['reminder_id']}, "
                        #           f"用户ID: {reminder_dict['user_id']}, "
                        #           f"触发时间: {reminder_dict['next_trigger_time']}")

                        # 1. 生成提醒内容
                        reminder_content = self.generate_reminder_content(reminder_dict)

                        # 2. 推送提醒
                        self.send_reminder_to_user(reminder_dict["user_id"], reminder_content)

                        # 3. 更新提醒状态为completed，避免重复推送
                        update_reminder_status(reminder_dict["reminder_id"], reminder_dict["user_id"], "completed")
                else:
                    # logger.info("当前没有需要处理的提醒")
                    pass
            except Exception as e:
                logger.error(f"后台提醒任务执行失败: {e}")

            # 休眠10分钟 (600秒)
            time.sleep(600)

    def start_reminder_thread(self):
        """
        启动提醒后台线程
        """
        global reminder_thread, thread_start_time

        if reminder_thread and reminder_thread.is_alive():
            logger.warning("提醒线程已经在运行中")
            return False

        reminder_thread = threading.Thread(target=self.reminder_background_task, daemon=True)
        reminder_thread.start()
        thread_start_time = datetime.now()
        logger.info("后台提醒检查线程已启动")
        return True

    def is_reminder_thread_alive(self):
        """
        检查提醒线程是否还活着
        """
        global reminder_thread
        return reminder_thread is not None and reminder_thread.is_alive()

    def get_reminder_thread_status(self):
        """
        获取提醒线程状态信息
        """
        is_alive = self.is_reminder_thread_alive()
        status_message = "线程正常运行" if is_alive else "线程已停止"
        start_time_str = thread_start_time.strftime("%Y-%m-%d %H:%M:%S") if thread_start_time else None

        return {
            "is_alive": is_alive,
            "start_time": start_time_str,
            "status_message": status_message,
        }

    def restart_reminder_thread(self):
        """
        重启后台提醒线程
        """
        try:
            success = self.start_reminder_thread()
            if success:
                return {"success": True, "message": "后台提醒线程重启成功"}
            else:
                return {"success": False, "message": "线程已在运行中，无需重启"}
        except Exception as e:
            logger.error(f"重启提醒线程失败: {e}")
            return {"success": False, "message": f"重启失败: {str(e)}"}

    def generate_reminder_content(self, reminder_dict):
        """生成提醒内容"""
        # 直接使用原始的提醒内容，不再调用大模型生成
        reminder_text = reminder_dict["reminder_text_template"]
        if not reminder_text:
            return "提醒时间到了！"

        # 添加时间信息到提醒内容
        base_event_date = reminder_dict.get("base_event_date")
        if base_event_date:
            # 格式化时间显示
            if isinstance(base_event_date, datetime):
                time_str = base_event_date.strftime("%H:%M")
            else:
                # 如果是字符串，尝试解析
                try:
                    parsed_time = datetime.strptime(str(base_event_date), "%Y-%m-%d %H:%M:%S")
                    time_str = parsed_time.strftime("%H:%M")
                except:
                    time_str = str(base_event_date)

            return f"⏰ 提醒：{reminder_text}（时间：{time_str}）"

        # 如果没有时间信息，返回原始内容
        return f"⏰ 提醒：{reminder_text}"

    def send_reminder_to_user(self, user_id, content):
        """推送提醒给用户"""
        # 这里可以是发微信、短信、App通知等
        logger.info(f"推送给用户{user_id}的提醒内容：{content}")

    def add_reminder(self, request_data: dict):
        """
        添加一条提醒到MySQL reminders表
        """
        # 生成温馨展示内容
        display_text = generate_display_text(
            request_data.get("reminder_text_template"),
            request_data.get("next_trigger_time"),
            request_data.get("base_event_date"),
        )

        # 处理advance_notice_config，确保是JSON字符串
        advance_notice_config = request_data.get("advance_notice_config")
        if isinstance(advance_notice_config, dict):
            advance_notice_config = json.dumps(advance_notice_config)

        reminder_id = insert_reminder(
            user_id=request_data.get("user_id"),
            base_event_date=request_data.get("base_event_date"),
            next_trigger_time=request_data.get("next_trigger_time"),
            subject_person_id=request_data.get("subject_person_id"),
            reminder_text_template=request_data.get("reminder_text_template"),
            advance_notice_config=advance_notice_config,
            recurrence_rule=request_data.get("recurrence_rule"),
            status=request_data.get("status", "active"),
            display_text=display_text,
        )

        logger.info(
            f"插入数据库内容: reminder_id={reminder_id}, user_id={request_data.get('user_id')}, "
            f"next_trigger_time={request_data.get('next_trigger_time')}, "
            f"base_event_date={request_data.get('base_event_date')}, "
            f"subject_person_id={request_data.get('subject_person_id')}, "
            f"reminder_text_template={request_data.get('reminder_text_template')}, "
            f"advance_notice_config={request_data.get('advance_notice_config')}, "
            f"recurrence_rule={request_data.get('recurrence_rule')}, "
            f"status={request_data.get('status', 'active')}, display_text={display_text}"
        )

        if reminder_id:
            return {"success": True, "reminder_id": reminder_id}
        else:
            return {"success": False, "message": "插入失败"}

    def delete_reminder(self, reminder_id: int, user_id: str):
        """
        删除指定用户的提醒，确保用户隔离
        """
        success = delete_reminder_by_id(reminder_id, user_id)
        if success:
            return {"success": True, "message": "删除成功"}
        else:
            return {"success": False, "message": "删除失败或提醒不存在"}

    def list_reminders(self, user_id: str):
        """
        获取指定用户的所有提醒列表，确保用户隔离
        """
        try:
            reminders_raw = query_reminders_by_user(user_id)

            # 将 SQLAlchemy Row 对象转换为字典
            reminders = []
            for reminder in reminders_raw:
                if hasattr(reminder, "_asdict"):
                    # 如果是 NamedTuple 类型
                    reminder_dict = reminder._asdict()
                elif hasattr(reminder, "keys"):
                    # 如果是 Row 对象
                    reminder_dict = dict(reminder)
                else:
                    # 如果已经是字典
                    reminder_dict = reminder

                # 处理 JSON 字段
                if "advance_notice_config" in reminder_dict and reminder_dict["advance_notice_config"]:
                    try:
                        reminder_dict["advance_notice_config"] = json.loads(reminder_dict["advance_notice_config"])
                    except:
                        pass  # 如果解析失败，保持原值

                reminders.append(reminder_dict)

            return {"success": True, "reminders": reminders, "count": len(reminders)}
        except Exception as e:
            logger.error(f"获取用户提醒列表失败: {str(e)}")
            return {"success": False, "message": f"获取提醒列表失败: {str(e)}", "reminders": [], "count": 0}

    def update_reminder(self, reminder_id: int, user_id: str, update_params: dict):
        """
        编辑指定用户的提醒，确保用户隔离
        """
        # 只要内容/时间有变化就生成display_text
        if (
            "reminder_text_template" in update_params
            or "next_trigger_time" in update_params
            or "base_event_date" in update_params
        ):
            old = query_reminder_by_id(reminder_id, user_id)
            reminder_text_val = update_params.get("reminder_text_template") or (old and old.get("reminder_text_template"))
            base_event_date_val = update_params.get("base_event_date") or (old and old.get("base_event_date"))
            next_trigger_time_val = update_params.get("next_trigger_time") or (old and old.get("next_trigger_time"))

            # 确保时间参数是datetime对象
            if isinstance(base_event_date_val, str):
                # 支持ISO格式和标准格式
                try:
                    base_event_date_val = datetime.fromisoformat(base_event_date_val.replace("T", " ").replace("Z", ""))
                except:
                    base_event_date_val = datetime.strptime(base_event_date_val, "%Y-%m-%d %H:%M:%S")
            if isinstance(next_trigger_time_val, str):
                # 支持ISO格式和标准格式
                try:
                    next_trigger_time_val = datetime.fromisoformat(next_trigger_time_val.replace("T", " ").replace("Z", ""))
                except:
                    next_trigger_time_val = datetime.strptime(next_trigger_time_val, "%Y-%m-%d %H:%M:%S")

            display_text = generate_display_text(reminder_text_val, next_trigger_time_val, base_event_date_val)
            update_params["display_text"] = display_text

            # 自动计算并更新 advance_notice_config
            advance_notice_config = calculate_advance_notice_config(next_trigger_time_val, base_event_date_val)
            # 将字典转换为JSON字符串，因为数据库字段是TEXT类型
            if isinstance(advance_notice_config, dict):
                update_params["advance_notice_config"] = json.dumps(advance_notice_config)
            else:
                update_params["advance_notice_config"] = advance_notice_config

        success = db_update_reminder(reminder_id, user_id, **update_params)
        if success:
            logger.info(
                f"更新提醒成功: reminder_id={reminder_id}, user_id={user_id}, 更新字段={list(update_params.keys())}"
            )
            return {"result": "success"}
        else:
            return {"result": "error", "message": "更新失败或提醒不存在"}


# 数据模型
class AddReminderRequest(BaseModel):
    user_id: str
    base_event_date: datetime
    next_trigger_time: datetime
    subject_person_id: Optional[str] = None
    reminder_text_template: Optional[str] = None
    advance_notice_config: Optional[dict] = None
    recurrence_rule: Optional[str] = None
    status: str = "active"
    display_text: Optional[str] = None


class DeleteReminderRequest(BaseModel):
    user_id: str
    reminder_id: int


class UpdateReminderRequest(BaseModel):
    user_id: str
    reminder_id: int
    base_event_date: Optional[datetime] = None
    next_trigger_time: Optional[datetime] = None
    subject_person_id: Optional[str] = None
    reminder_text_template: Optional[str] = None
    advance_notice_config: Optional[dict] = None
    recurrence_rule: Optional[str] = None
    status: Optional[str] = None
    display_text: Optional[str] = None


# 全局实例
reminder_service = ReminderService()
