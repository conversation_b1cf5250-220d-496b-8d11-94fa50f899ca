# 智能提醒功能配置指南

## 功能概述

智能提醒功能可以自动从对话中识别需要设置提醒的事件，并自动创建相应的提醒。

## 配置参数

### Lion配置中心参数

```json
{
  "humanrelation.enable_smart_reminder": "true",
  "humanrelation.smart_reminder_model": "gpt-4o-mini",
  "humanrelation.smart_reminder_extraction_prompt": "智能提醒提取提示词"
}
```

### 参数说明

- `enable_smart_reminder`: 是否启用智能提醒功能（true/false）
- `smart_reminder_model`: 用于智能提醒分析的AI模型
- `smart_reminder_extraction_prompt`: AI提取提醒信息的提示词

## 支持的提醒类型

| 类型 | 说明 | 提前时间 | 示例 |
|------|------|----------|------|
| birthday | 生日提醒 | 7天前、1天前 | "明天是张三的生日" |
| meeting | 会议提醒 | 30分钟前、10分钟前 | "下午3点开会" |
| appointment | 约会提醒 | 60分钟前、15分钟前 | "明天和医生预约" |
| deadline | 截止日期 | 3天前、1天前 | "报告截止日期是周五" |
| event | 通用事件 | 24小时前、2小时前 | "演唱会是下周六" |
| travel | 出行提醒 | 7天前、1天前 | "下个月要去北京" |
| health | 健康提醒 | 12小时前、1小时前 | "明天要体检" |
| anniversary | 纪念日 | 7天前、1天前 | "结婚纪念日快到了" |

## API接口

### 1. 智能提醒提取和创建

```http
POST /humanrelation/smart_reminder_extract
Content-Type: application/json

{
  "user_id": "用户ID",
  "conversation_text": "对话文本内容",
  "auto_create": true
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "成功创建 2 个智能提醒",
  "reminders_created": 2,
  "reminder_ids": ["123", "124"],
  "extracted_info_count": 1,
  "successful_events": 1
}
```

### 2. 智能提醒分析（仅分析不创建）

```http
POST /humanrelation/smart_reminder_analysis
Content-Type: application/json

{
  "user_id": "用户ID",
  "conversation_text": "对话文本内容"
}
```

**响应示例：**
```json
{
  "result": "success",
  "message": "分析完成，发现 1 个潜在提醒",
  "user_id": "test_user",
  "extracted_count": 1,
  "analysis_result": [
    {
      "type": "meeting",
      "event_description": "和产品经理开会讨论新功能",
      "event_time": "2025-08-06T15:00:00",
      "person": "产品经理",
      "location": "会议室",
      "reminder_text": "📅 会议提醒：和产品经理开会讨论新功能，地点：会议室",
      "reminder_times_count": 2,
      "confidence": 0.85,
      "context": "明天下午三点"
    }
  ]
}
```

## 集成到聊天流程

智能提醒功能已自动集成到所有聊天接口中：

### 1. `/humanrelation/chat_json` 接口

**聊天响应示例：**
```json
{
  "response": "好的，我帮您记住明天下午3点的会议安排。",
  "status": "success",
  "recommended_questions": [...],
  "smart_reminder": {
    "success": true,
    "message": "成功创建 2 个智能提醒",
    "reminders_created": 2,
    "reminder_ids": ["125", "126"]
  }
}
```

### 2. `/humanrelation/chat` 流式接口

智能提醒结果会作为流式数据的一部分返回：

**流式响应示例：**
```
data: {"content": "好的，我帮您记住", "type": "chat", "is_final": false}

data: {"content": "好的，我帮您记住明天下午3点的会议安排。", "type": "chat", "is_final": true}

data: {"type": "recommendations", "questions": ["相关推荐问题..."]}

data: {"type": "smart_reminder", "result": {"success": true, "message": "成功创建 2 个智能提醒", "reminders_created": 2, "reminder_ids": ["125", "126"]}}
```

**流式数据类型说明：**
- `type: "chat"` - AI回复内容
- `type: "recommendations"` - 推荐问题
- `type: "smart_reminder"` - 智能提醒结果

## 测试示例

### 生日提醒测试
```json
{
  "user_id": "test_user",
  "conversation_text": "用户: 提醒我，下周三是我妈妈的生日\nAI: 好的，我会帮您设置生日提醒。"
}
```

### 会议提醒测试
```json
{
  "user_id": "test_user",
  "conversation_text": "用户: 明天下午2点我要和张总开会讨论项目进展\nAI: 收到，我会为您安排会议提醒。"
}
```

### 约会提醒测试
```json
{
  "user_id": "test_user",
  "conversation_text": "用户: 我下周五上午10点要去医院体检\nAI: 好的，体检很重要，我会提醒您的。"
}
```

## 注意事项

1. **时间格式**：支持多种自然语言时间表达，如"明天下午3点"、"下周五"等
2. **置信度**：AI分析的置信度低于0.6的事件不会创建提醒
3. **过期时间**：只会为未来时间的事件创建提醒
4. **错误处理**：智能提醒失败不会影响正常聊天功能
5. **配置控制**：可通过配置参数随时启用/禁用功能

## 故障排除

1. **提醒未创建**：检查AI分析的置信度和时间是否为未来时间
2. **AI调用失败**：检查AI模型配置和网络连接
3. **功能禁用**：检查 `enable_smart_reminder` 配置参数
4. **提示词优化**：可以自定义 `smart_reminder_extraction_prompt` 提升识别准确率
